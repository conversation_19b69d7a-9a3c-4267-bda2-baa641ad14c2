# Google Cloud Build Configuration
# ملف تكوين Google Cloud Build لمشروع Universal-AI-Assistants

steps:
  # الخطوة 1: بناء صورة Docker الأساسية
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/universal-ai-assistants:latest',
      '-f', 'Dockerfile.production',
      '.'
    ]
    id: 'build-main-image'

  # الخطوة 2: بناء صورة ANUBIS_SYSTEM
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/anubis-system:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/anubis-system:latest',
      '-f', 'ANUBIS_SYSTEM/Dockerfile',
      './ANUBIS_SYSTEM'
    ]
    id: 'build-anubis'

  # الخطوة 3: بناء صورة HORUS_AI_TEAM
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/horus-ai-team:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/horus-ai-team:latest',
      '-f', 'HORUS_AI_TEAM/Dockerfile',
      './HORUS_AI_TEAM'
    ]
    id: 'build-horus'

  # الخطوة 4: بناء صورة ANUBIS_HORUS_MCP
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/anubis-horus-mcp:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/anubis-horus-mcp:latest',
      '-f', 'ANUBIS_HORUS_MCP/Dockerfile',
      './ANUBIS_HORUS_MCP'
    ]
    id: 'build-mcp'

  # الخطوة 5: بناء صورة Qwen3-Coder
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/qwen3-coder:$BUILD_ID',
      '-t', 'gcr.io/$PROJECT_ID/qwen3-coder:latest',
      '-f', 'Qwen3-Coder/Dockerfile',
      './Qwen3-Coder'
    ]
    id: 'build-qwen'

  # الخطوة 6: تشغيل الاختبارات
  - name: 'gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID'
    args: ['python', '-m', 'pytest', 'tests/', '-v']
    id: 'run-tests'
    waitFor: ['build-main-image']

  # الخطوة 7: فحص الأمان
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'run', '--rm',
      'aquasec/trivy:latest',
      'image',
      'gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID'
    ]
    id: 'security-scan'
    waitFor: ['build-main-image']

  # الخطوة 8: رفع الصور إلى Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID']
    id: 'push-main'
    waitFor: ['run-tests', 'security-scan']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/anubis-system:$BUILD_ID']
    id: 'push-anubis'
    waitFor: ['build-anubis']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/horus-ai-team:$BUILD_ID']
    id: 'push-horus'
    waitFor: ['build-horus']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/anubis-horus-mcp:$BUILD_ID']
    id: 'push-mcp'
    waitFor: ['build-mcp']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/qwen3-coder:$BUILD_ID']
    id: 'push-qwen'
    waitFor: ['build-qwen']

  # الخطوة 9: نشر على Google Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'universal-ai-assistants',
      '--image', 'gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '2Gi',
      '--cpu', '2',
      '--max-instances', '10',
      '--set-env-vars', 'PROJECT_ID=$PROJECT_ID,BUILD_ID=$BUILD_ID'
    ]
    id: 'deploy-main'
    waitFor: ['push-main']

  # الخطوة 10: نشر خدمات إضافية
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'anubis-system',
      '--image', 'gcr.io/$PROJECT_ID/anubis-system:$BUILD_ID',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--memory', '1Gi',
      '--cpu', '1'
    ]
    id: 'deploy-anubis'
    waitFor: ['push-anubis']

  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'horus-ai-team',
      '--image', 'gcr.io/$PROJECT_ID/horus-ai-team:$BUILD_ID',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--memory', '1Gi',
      '--cpu', '1'
    ]
    id: 'deploy-horus'
    waitFor: ['push-horus']

# إعدادات إضافية
options:
  # استخدام آلة قوية للبناء
  machineType: 'E2_HIGHCPU_8'
  # زيادة مهلة البناء
  timeout: '3600s'
  # تسجيل مفصل
  logging: CLOUD_LOGGING_ONLY
  
# متغيرات البيئة
substitutions:
  _DEPLOY_REGION: 'us-central1'
  _SERVICE_NAME: 'universal-ai-assistants'

# الصور المنتجة
images:
  - 'gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/universal-ai-assistants:latest'
  - 'gcr.io/$PROJECT_ID/anubis-system:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/horus-ai-team:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/anubis-horus-mcp:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/qwen3-coder:$BUILD_ID'

# العلامات للتنظيم
tags: ['universal-ai-assistants', 'ai-system', 'production']
