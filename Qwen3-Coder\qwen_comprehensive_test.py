#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنموذج Qwen3-Coder
يختبر قدرات النموذج في مختلف المجالات البرمجية
"""

import sys
import time
import json
from datetime import datetime
from qwen_openrouter import ask_qwen_coder

# مفتاح API (يمكن تمريره كمعامل)
API_KEY = "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"

class QwenTester:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        
    def run_test(self, test_name: str, prompt: str, expected_keywords: list = None):
        """
        تشغيل اختبار واحد
        """
        print(f"\n🧪 اختبار: {test_name}")
        print("=" * 50)
        print(f"📝 السؤال: {prompt}")
        print("\n⏳ جاري المعالجة...")
        
        start_time = time.time()
        try:
            response = ask_qwen_coder(prompt, self.api_key)
            end_time = time.time()
            response_time = round(end_time - start_time, 2)
            
            # تقييم الاستجابة
            success = True
            if expected_keywords:
                for keyword in expected_keywords:
                    if keyword.lower() not in response.lower():
                        success = False
                        break
            
            # حفظ النتيجة
            result = {
                "test_name": test_name,
                "prompt": prompt,
                "response": response,
                "response_time": response_time,
                "success": success,
                "expected_keywords": expected_keywords,
                "timestamp": datetime.now().isoformat()
            }
            
            self.test_results.append(result)
            self.total_tests += 1
            if success:
                self.passed_tests += 1
            
            # عرض النتيجة
            status = "✅ نجح" if success else "❌ فشل"
            print(f"\n📊 النتيجة: {status}")
            print(f"⏱️ وقت الاستجابة: {response_time} ثانية")
            print(f"\n💬 الاستجابة:")
            print("-" * 30)
            print(response)
            print("-" * 30)
            
            return result
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            return None
    
    def run_comprehensive_test(self):
        """
        تشغيل مجموعة اختبارات شاملة
        """
        print("🚀 بدء الاختبار الشامل لنموذج Qwen3-Coder")
        print("=" * 60)
        
        # اختبار 1: البرمجة الأساسية
        self.run_test(
            "البرمجة الأساسية - Python",
            "اكتب دالة Python لحساب العدد الأولي",
            ["def", "prime", "return"]
        )
        
        # اختبار 2: هياكل البيانات
        self.run_test(
            "هياكل البيانات",
            "اكتب كلاس Python لتنفيذ Stack (مكدس) مع العمليات الأساسية",
            ["class", "stack", "push", "pop"]
        )
        
        # اختبار 3: خوارزميات
        self.run_test(
            "الخوارزميات",
            "اكتب خوارزمية الفرز السريع (Quick Sort) في Python",
            ["quicksort", "sort", "partition"]
        )
        
        # اختبار 4: JavaScript
        self.run_test(
            "JavaScript",
            "اكتب دالة JavaScript لتحويل JSON إلى HTML table",
            ["function", "json", "html", "table"]
        )
        
        # اختبار 5: قواعد البيانات
        self.run_test(
            "قواعد البيانات - SQL",
            "اكتب استعلام SQL لإيجاد أعلى 5 موظفين راتباً من جدول employees",
            ["select", "top", "order by", "desc"]
        )
        
        # اختبار 6: تطوير الويب
        self.run_test(
            "تطوير الويب - HTML/CSS",
            "اكتب صفحة HTML بسيطة مع CSS لعرض قائمة منتجات",
            ["html", "css", "div", "style"]
        )
        
        # اختبار 7: معالجة الأخطاء
        self.run_test(
            "معالجة الأخطاء",
            "اكتب دالة Python تقرأ ملف وتتعامل مع الأخطاء المحتملة",
            ["try", "except", "file", "open"]
        )
        
        # اختبار 8: البرمجة الكائنية
        self.run_test(
            "البرمجة الكائنية",
            "اكتب كلاس Python لنظام إدارة المكتبة مع الوراثة",
            ["class", "inheritance", "library", "__init__"]
        )
        
        return self.generate_report()
    
    def generate_report(self):
        """
        إنشاء تقرير شامل للاختبارات
        """
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        report = {
            "test_summary": {
                "total_tests": self.total_tests,
                "passed_tests": self.passed_tests,
                "failed_tests": self.total_tests - self.passed_tests,
                "success_rate": round(success_rate, 2),
                "test_date": datetime.now().isoformat()
            },
            "detailed_results": self.test_results
        }
        
        # حفظ التقرير
        report_filename = f"qwen_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # عرض الملخص
        print("\n" + "=" * 60)
        print("📊 تقرير الاختبار الشامل")
        print("=" * 60)
        print(f"📈 إجمالي الاختبارات: {self.total_tests}")
        print(f"✅ الاختبارات الناجحة: {self.passed_tests}")
        print(f"❌ الاختبارات الفاشلة: {self.total_tests - self.passed_tests}")
        print(f"📊 معدل النجاح: {success_rate:.2f}%")
        print(f"💾 تم حفظ التقرير في: {report_filename}")
        
        return report

def main():
    """
    الدالة الرئيسية
    """
    if len(sys.argv) > 1:
        api_key = sys.argv[1]
    else:
        api_key = API_KEY
    
    print("🤖 مرحباً بك في نظام اختبار Qwen3-Coder الشامل!")
    print("🎯 سيتم اختبار قدرات النموذج في 8 مجالات مختلفة")
    
    tester = QwenTester(api_key)
    report = tester.run_comprehensive_test()
    
    print(f"\n🎉 تم إكمال جميع الاختبارات!")
    print(f"📊 معدل النجاح النهائي: {report['test_summary']['success_rate']}%")

if __name__ == "__main__":
    main()
